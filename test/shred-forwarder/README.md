# ShredForwarder Client Tests

This directory contains comprehensive tests for the ShredForwarderClient, including mock server implementation and client testing with resubscribe mechanism verification.

## Files

### `server.ts` - Mock gRPC Server
A complete mock implementation of the ShredForwarder gRPC service that:
- Implements both `ping` (unary) and `subscribe` (streaming) methods
- Supports connection drop simulation for testing reconnection
- Provides configurable message intervals and timeouts
- Can run standalone for manual testing

### `client.ts` - Client Test Suite
Comprehensive test suite that verifies:
- **Unary Methods**: Tests ping functionality with proper request/response handling
- **Streaming**: Tests basic streaming without reconnection
- **Resubscribe Mechanism**: Tests automatic reconnection after connection failures
  - Simulates connection drops
  - Verifies automatic reconnection
  - Confirms stream continuity after reconnection
  - Logs detailed resubscribe events

### `debug-resubscribe.ts` - Debug Script
Simplified debug script for troubleshooting resubscribe mechanism with detailed logging.

## Running Tests

### Run Complete Test Suite
```bash
tsx test/shred-forwarder/client.ts
```

### Run Mock Server Only
```bash
tsx test/shred-forwarder/server.ts
```

### Run Debug Script
```bash
tsx test/shred-forwarder/debug-resubscribe.ts
```

## Test Features

### Mock Server Capabilities
- **Ping Method**: Returns count, timestamp, and version
- **Subscribe Stream**: Sends mock transaction data with configurable intervals
- **Connection Drop Simulation**: Can simulate network failures after N messages
- **Event Logging**: Detailed server-side event logging

### Client Test Coverage
- **Unary Method Testing**: Verifies ping request/response cycle
- **Basic Streaming**: Tests stream subscription and data reception
- **Reconnection Testing**: 
  - Enables connection drops after receiving messages
  - Monitors resubscribe events (`waitForResubscribe`, `resubscribe`, `resubscribed`)
  - Verifies stream continuity after reconnection
  - Tracks reconnection count and message count

### Resubscribe Mechanism Verification
The tests specifically verify:
1. **Connection Drop Detection**: Stream properly detects connection failures
2. **Automatic Resubscribe**: StreamWrapper automatically attempts reconnection
3. **Exponential Backoff**: Resubscriber uses proper delay and backoff
4. **Stream Continuity**: Data flow resumes after successful reconnection
5. **Event Emission**: All resubscribe events are properly emitted

## Test Output Example

```
🚀 Starting ShredForwarder Client Tests
✅ Mock server started

=== Testing Unary Methods ===
[Client Test] ✅ Ping test passed

=== Testing Streaming (No Reconnection) ===
[Client Test] ✅ Stream subscribed successfully
[Client Test] ✅ Stream closed successfully
[Client Test] Received 5 messages in 5 seconds

=== Testing Resubscribe Mechanism ===
[Client Test] ✅ Initial subscription successful
[Client Test] 🔥 Enabling connection drops after 3 messages
[Client Test] Stream error: 2 UNKNOWN: Simulated connection drop
[Client Test] 🔄 Waiting 1000ms before resubscribe
[Client Test] 🔄 Resubscribe attempt 1, retries left: 2
[Client Test] ✅ Successfully resubscribed!
[Client Test] ✅ Reconnection test completed
[Client Test] Total messages: 6, Reconnections: 1

🎉 All tests completed!
```

## Configuration

### Mock Server Options
- `port`: Server port (default: 50051)
- `simulateConnectionDrops`: Enable/disable connection drop simulation
- `dropAfterMessages`: Number of messages before dropping connection
- `subscribeInterval`: Interval between messages (ms)
- `pingDelay`: Delay for ping responses (ms)

### Client Test Options
- `serverPort`: Port to connect to (default: 50052)
- `testDuration`: Total test duration (ms)
- `enableReconnectionTest`: Enable/disable reconnection testing
- `reconnectionTestDelay`: How long to wait for reconnection

## Architecture

The tests follow the existing project patterns:
- Use ESM modules with single-line imports
- Follow the established client architecture patterns
- Use consistent error handling and logging
- Integrate with existing StreamWrapper and Resubscriber classes
- Test the actual resubscribe mechanism as implemented in the codebase
