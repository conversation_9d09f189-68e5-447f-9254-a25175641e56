/* eslint-disable */
import { ShredForwarderClient } from '../../src/clients/shred-forwarder'
import { MockShredForwarderServer } from './server'

interface TestOptions {
    serverPort?: number
    testDuration?: number
    enableReconnectionTest?: boolean
    reconnectionTestDelay?: number
}

class ShredForwarderClientTester {
    private readonly client: ShredForwarderClient
    private readonly server: MockShredForwarderServer
    private readonly serverPort: number
    private readonly testDuration: number
    private readonly enableReconnectionTest: boolean
    private readonly reconnectionTestDelay: number

    constructor(options: TestOptions = {}) {
        this.serverPort = options.serverPort ?? 50_052
        this.testDuration = options.testDuration ?? 30_000
        this.enableReconnectionTest = options.enableReconnectionTest ?? true
        this.reconnectionTestDelay = options.reconnectionTestDelay ?? 10_000

        this.server = new MockShredForwarderServer({
            port: this.serverPort,
            simulateConnectionDrops: false,
            subscribeInterval: 1000,
        })

        this.client = new ShredForwarderClient(`http://localhost:${this.serverPort}`)
    }

    async testUnaryMethods() {
        console.log('\n=== Testing Unary Methods ===')

        try {
            console.log('[Client Test] Testing ping method...')
            const pingResponse = await this.client.ping({ count: 42 })

            console.log('[Client Test] Ping response:', {
                count: pingResponse.count,
                timestamp: pingResponse.timestamp?.toISOString(),
                version: pingResponse.version,
            })

            console.log('[Client Test] ✅ Ping test passed')
        } catch (error) {
            console.error('[Client Test] ❌ Ping test failed:', error)
        }
    }

    async testStreamingWithoutReconnection() {
        console.log('\n=== Testing Streaming (No Reconnection) ===')

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000,
            },
            resubscribe: false,
        })

        let messageCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Client Test] Message ${messageCount}: slot=${data.slot}, entryIndex=${data.entryIndex}`)
        })

        stream.on('state', (state) => {
            console.log(`[Client Test] Stream state: ${state}`)
        })

        stream.on('error', (error) => {
            console.error('[Client Test] Stream error:', error.message)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Client Test] Stream closed - explicitly: ${isExplicitly}`)

            if (error) {
                console.log('[Client Test] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Client Test] ✅ Stream subscribed successfully')

            await new Promise((resolve) => setTimeout(resolve, 5000))

            console.log('[Client Test] Closing stream...')
            await stream.close()
            console.log('[Client Test] ✅ Stream closed successfully')
            console.log(`[Client Test] Received ${messageCount} messages in 5 seconds`)
        } catch (error) {
            console.error('[Client Test] ❌ Streaming test failed:', error)
        }
    }

    async testResubscribeMechanism() {
        if (!this.enableReconnectionTest) {
            console.log('\n=== Skipping Reconnection Test (disabled) ===')

            return
        }

        console.log('\n=== Testing Resubscribe Mechanism ===')

        this.server.enableConnectionDrops(3)

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000,
            },
            resubscribe: {
                enabled: true,
                retries: 3,
                delay: 1000,
                backoff: 1.5,
                maxDelay: 5000,
                autoReset: true,
            },
        })

        let messageCount = 0
        let reconnectionCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Client Test] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)
        })

        stream.on('state', (state) => {
            console.log(`[Client Test] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Client Test] Stream error: ${error.message} (${Date.now() - startTime}ms)`)
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Client Test] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Client Test] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Client Test] ✅ Successfully resubscribed! (${Date.now() - startTime}ms)`)
            this.server.disableConnectionDrops()
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Client Test] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Client Test] ❌ Resubscribe abandoned: ${reason}`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Client Test] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)

            if (error) {
                console.log('[Client Test] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Client Test] ✅ Initial subscription successful')

            await new Promise<void>((resolve) => {
                let hasReconnected = false

                const onResubscribed = () => {
                    if (!hasReconnected) {
                        hasReconnected = true
                        console.log(`[Client Test] 🎯 Detected successful reconnection, waiting 3 more seconds...`)
                        setTimeout(() => resolve(), 3000)
                    }
                }

                stream.once('resubscribed', onResubscribed)

                setTimeout(() => {
                    if (!hasReconnected) {
                        console.log(`[Client Test] ⏰ Timeout reached without reconnection`)
                        stream.off('resubscribed', onResubscribed)
                        resolve()
                    }
                }, this.reconnectionTestDelay)
            })

            console.log('[Client Test] Closing stream after reconnection test...')
            await stream.close()
            console.log('[Client Test] ✅ Reconnection test completed')
            console.log(`[Client Test] Total messages: ${messageCount}, Reconnections: ${reconnectionCount}`)

            if (reconnectionCount === 0) {
                console.log('[Client Test] ⚠️  Warning: No reconnections occurred during test')
            }
        } catch (error) {
            console.error('[Client Test] ❌ Reconnection test failed:', error)
        }
    }

    public async runAllTests() {
        console.log('🚀 Starting ShredForwarder Client Tests')
        console.log(`Server: localhost:${this.serverPort}`)
        console.log(`Test duration: ${this.testDuration}ms`)
        console.log(`Reconnection test: ${this.enableReconnectionTest ? 'enabled' : 'disabled'}`)

        try {
            await this.server.start()
            console.log('✅ Mock server started')

            await this.testUnaryMethods()
            await this.testStreamingWithoutReconnection()
            await this.testResubscribeMechanism()

            console.log('\n🎉 All tests completed!')
        } catch (error) {
            console.error('❌ Test suite failed:', error)
        } finally {
            await this.cleanup()
        }
    }

    public async cleanup() {
        console.log('\n🧹 Cleaning up...')

        try {
            this.client.grpc.close()
            await this.server.stop()
            console.log('✅ Cleanup completed')
        } catch (error) {
            console.error('❌ Cleanup failed:', error)
        }
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new ShredForwarderClientTester({
        serverPort: 50_052,
        testDuration: 30_000,
        enableReconnectionTest: true,
        reconnectionTestDelay: 15_000,
    })

    process.on('SIGINT', async () => {
        console.log('\n🛑 Received SIGINT, cleaning up...')
        await tester.cleanup()
        process.exit(0)
    })

    tester.runAllTests().catch(console.error)
}
