/* eslint-disable */
import { ShredForwarderClient } from '../../src/clients/shred-forwarder'
import { MockShredForwarderServer } from './server'

interface TestOptions {
    serverPort?: number
    testDuration?: number
    enableReconnectionTest?: boolean
    reconnectionTestDelay?: number
}

class ShredForwarderClientTester {
    private readonly client: ShredForwarderClient
    private server: MockShredForwarderServer
    private readonly serverPort: number
    private readonly testDuration: number
    private readonly enableReconnectionTest: boolean
    private readonly reconnectionTestDelay: number

    constructor(options: TestOptions = {}) {
        this.serverPort = options.serverPort ?? 50_052
        this.testDuration = options.testDuration ?? 30_000
        this.enableReconnectionTest = options.enableReconnectionTest ?? true
        this.reconnectionTestDelay = options.reconnectionTestDelay ?? 10_000

        this.server = new MockShredForwarderServer({
            port: this.serverPort,
            simulateConnectionDrops: false,
            subscribeInterval: 1000,
        })

        this.client = new ShredForwarderClient(`http://localhost:${this.serverPort}`)
    }

    async testUnaryMethods() {
        console.log('\n=== Testing Unary Methods ===')

        try {
            console.log('[Client Test] Testing ping method...')
            const pingResponse = await this.client.ping({ count: 42 })

            console.log('[Client Test] Ping response:', {
                count: pingResponse.count,
                timestamp: pingResponse.timestamp?.toISOString(),
                version: pingResponse.version,
            })

            console.log('[Client Test] ✅ Ping test passed')
        } catch (error) {
            console.error('[Client Test] ❌ Ping test failed:', error)
        }
    }

    async testStreamingWithoutReconnection() {
        console.log('\n=== Testing Streaming (No Reconnection) ===')

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000,
            },
            resubscribe: false,
        })

        let messageCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Client Test] Message ${messageCount}: slot=${data.slot}, entryIndex=${data.entryIndex}`)
        })

        stream.on('state', (state) => {
            console.log(`[Client Test] Stream state: ${state}`)
        })

        stream.on('error', (error) => {
            console.error('[Client Test] Stream error:', error.message)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Client Test] Stream closed - explicitly: ${isExplicitly}`)

            if (error) {
                console.log('[Client Test] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Client Test] ✅ Stream subscribed successfully')

            await new Promise((resolve) => setTimeout(resolve, 5000))

            console.log('[Client Test] Closing stream...')
            await stream.close()
            console.log('[Client Test] ✅ Stream closed successfully')
            console.log(`[Client Test] Received ${messageCount} messages in 5 seconds`)
        } catch (error) {
            console.error('[Client Test] ❌ Streaming test failed:', error)
        }
    }

    async testConnectionDroppedException() {
        console.log('\n=== Testing Connection Dropped Exception ===')

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000
            },
            resubscribe: {
                enabled: true,
                retries: 5,
                delay: 1000,
                backoff: 1.5,
                maxDelay: 5000,
                autoReset: true
            }
        })

        let messageCount = 0
        let reconnectionCount = 0
        let exceptionThrown = false
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Connection Drop] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)

            if (messageCount === 2) {
                console.log('[Connection Drop] 💀 Simulating abrupt connection drop (14 UNAVAILABLE)')
                setTimeout(() => {
                    try {
                        // Simulate the exact scenario from your real app
                        // Force close the underlying connection without proper cleanup
                        const grpcStream = (stream as any).stream
                        if (grpcStream) {
                            // Emit the exact error you're seeing in production
                            const unavailableError = new Error('14 UNAVAILABLE: Connection dropped') as any
                            unavailableError.code = 14
                            unavailableError.details = 'Connection dropped'

                            console.log('[Connection Drop] 💀 Emitting UNAVAILABLE error directly to stream')
                            grpcStream.emit('error', unavailableError)

                            // Also destroy the stream to simulate abrupt disconnection
                            setTimeout(() => {
                                grpcStream.destroy()
                            }, 50)
                        }
                    } catch (error) {
                        console.error('[Connection Drop] Error during connection drop simulation:', error)
                    }
                }, 100)
            }
        })

        stream.on('state', (state) => {
            console.log(`[Connection Drop] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Connection Drop] Stream error: ${error} (${Date.now() - startTime}ms)`)

            // Check if this is the UNAVAILABLE error we're looking for
            if (error.toString().includes('14 UNAVAILABLE') || error.toString().includes('Connection dropped')) {
                console.log('[Connection Drop] 🎯 Detected UNAVAILABLE error - this matches production issue!')
                exceptionThrown = true
            }
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Connection Drop] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Connection Drop] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Connection Drop] ✅ Successfully resubscribed after connection drop! (${Date.now() - startTime}ms)`)
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Connection Drop] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Connection Drop] ❌ Resubscribe abandoned: ${reason} (${Date.now() - startTime}ms)`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Connection Drop] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)
            if (error) {
                console.log('[Connection Drop] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Connection Drop] ✅ Initial subscription successful')

            // Wait for the connection drop to happen and see how it's handled
            await new Promise<void>((resolve) => {
                setTimeout(() => {
                    console.log(`[Connection Drop] ⏰ Test timeout reached`)
                    resolve()
                }, 10000)
            })

            console.log('[Connection Drop] Closing stream after connection drop test...')

            try {
                await stream.close()
                console.log('[Connection Drop] ✅ Stream closed successfully')
            } catch (closeError) {
                console.error('[Connection Drop] Error closing stream:', closeError)
            }

            console.log('[Connection Drop] ✅ Connection drop test completed')
            console.log(`[Connection Drop] Total messages: ${messageCount}, Reconnections: ${reconnectionCount}`)
            console.log(`[Connection Drop] Exception thrown: ${exceptionThrown}`)

            if (exceptionThrown && reconnectionCount === 0) {
                console.log('[Connection Drop] 🚨 ISSUE REPRODUCED: Exception thrown but no reconnection attempted!')
            }
        } catch (error) {
            console.error('[Connection Drop] ❌ Connection drop test failed with exception:', error)
            console.log('[Connection Drop] 🚨 This might be the same issue as in production!')
            exceptionThrown = true
        }
    }

    async testServerProcessKill() {
        console.log('\n=== Testing Server Process Kill (Abrupt Kill) ===')

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000
            },
            resubscribe: {
                enabled: true,
                retries: 5,
                delay: 1000,
                backoff: 1.5,
                maxDelay: 5000,
                autoReset: true
            }
        })

        let messageCount = 0
        let reconnectionCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Process Kill] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)

            if (messageCount === 3) {
                console.log('[Process Kill] 💀 Simulating abrupt server process kill (no notification to client)')
                setTimeout(() => {
                    try {
                        // Simulate abrupt process kill by destroying the server without any cleanup
                        // This mimics what happens when server process is killed with kill -9
                        process.kill(process.pid, 'SIGUSR1') // Just to trigger something, but we'll destroy server directly

                        // Destroy server internals to simulate process death
                        this.server.forceKill()

                        console.log('[Process Kill] 💀 Server process killed abruptly (no graceful shutdown)')

                        // Restart server after delay to test reconnection
                        setTimeout(async () => {
                            console.log('[Process Kill] 🔄 Restarting server after process kill...')
                            this.server = new MockShredForwarderServer({
                                port: this.serverPort,
                                simulateConnectionDrops: false,
                                subscribeInterval: 1000
                            })
                            await this.server.start()
                            console.log('[Process Kill] ✅ Server restarted after process kill')
                        }, 5000) // Longer delay to simulate real restart time
                    } catch (error) {
                        console.error('[Process Kill] Error during process kill simulation:', error)
                    }
                }, 100)
            }
        })

        stream.on('state', (state) => {
            console.log(`[Process Kill] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Process Kill] Stream error: ${error} (${Date.now() - startTime}ms)`)
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Process Kill] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Process Kill] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Process Kill] ✅ Successfully resubscribed after process kill! (${Date.now() - startTime}ms)`)
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Process Kill] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Process Kill] ❌ Resubscribe abandoned: ${reason} (${Date.now() - startTime}ms)`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Process Kill] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)
            if (error) {
                console.log('[Process Kill] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Process Kill] ✅ Initial subscription successful')

            await new Promise<void>((resolve) => {
                let hasReconnected = false

                const onResubscribed = () => {
                    if (!hasReconnected) {
                        hasReconnected = true
                        console.log(`[Process Kill] 🎯 Detected successful reconnection after process kill, waiting 3 more seconds...`)
                        setTimeout(() => resolve(), 3000)
                    }
                }

                stream.once('resubscribed', onResubscribed)

                setTimeout(() => {
                    if (!hasReconnected) {
                        console.log(`[Process Kill] ⏰ Timeout reached without reconnection`)
                        stream.off('resubscribed', onResubscribed)
                        resolve()
                    }
                }, 25000) // Longer timeout for process kill scenario
            })

            console.log('[Process Kill] Closing stream after process kill test...')
            await stream.close()
            console.log('[Process Kill] ✅ Process kill test completed')
            console.log(`[Process Kill] Total messages: ${messageCount}, Reconnections: ${reconnectionCount}`)

            if (reconnectionCount === 0) {
                console.log('[Process Kill] ⚠️  Warning: No reconnections occurred during process kill test')
            }
        } catch (error) {
            console.error('[Process Kill] ❌ Process kill test failed:', error)
        }
    }

    async testServerKillScenario() {
        console.log('\n=== Testing Server Kill Scenario ===')

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000
            },
            resubscribe: {
                enabled: true,
                retries: 10,
                delay: 500,
                backoff: 1.2,
                maxDelay: 2000,
                autoReset: true
            }
        })

        let messageCount = 0
        let reconnectionCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Kill Test] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)

            if (messageCount === 3) {
                console.log('[Kill Test] 💀 Killing server abruptly after 3 messages')
                setTimeout(async () => {
                    try {
                        this.server.forceKill()
                        console.log('[Kill Test] 💀 Server killed forcefully')

                        setTimeout(async () => {
                            console.log('[Kill Test] 🔄 Restarting server after 3 seconds...')
                            this.server = new MockShredForwarderServer({
                                port: this.serverPort,
                                simulateConnectionDrops: false,
                                subscribeInterval: 1000
                            })
                            await this.server.start()
                            console.log('[Kill Test] ✅ Server restarted')
                        }, 3000)
                    } catch (error) {
                        console.error('[Kill Test] Error during server kill:', error)
                    }
                }, 100)
            }
        })

        stream.on('state', (state) => {
            console.log(`[Kill Test] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Kill Test] Stream error: ${error} (${Date.now() - startTime}ms)`)
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Kill Test] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Kill Test] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Kill Test] ✅ Successfully resubscribed after server restart! (${Date.now() - startTime}ms)`)
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Kill Test] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Kill Test] ❌ Resubscribe abandoned: ${reason} (${Date.now() - startTime}ms)`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Kill Test] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)
            if (error) {
                console.log('[Kill Test] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Kill Test] ✅ Initial subscription successful')

            await new Promise<void>((resolve) => {
                let hasReconnected = false

                const onResubscribed = () => {
                    if (!hasReconnected) {
                        hasReconnected = true
                        console.log(`[Kill Test] 🎯 Detected successful reconnection after server restart, waiting 3 more seconds...`)
                        setTimeout(() => resolve(), 3000)
                    }
                }

                stream.once('resubscribed', onResubscribed)

                setTimeout(() => {
                    if (!hasReconnected) {
                        console.log(`[Kill Test] ⏰ Timeout reached without reconnection`)
                        stream.off('resubscribed', onResubscribed)
                        resolve()
                    }
                }, 20000)
            })

            console.log('[Kill Test] Closing stream after server kill test...')
            await stream.close()
            console.log('[Kill Test] ✅ Server kill test completed')
            console.log(`[Kill Test] Total messages: ${messageCount}, Reconnections: ${reconnectionCount}`)

            if (reconnectionCount === 0) {
                console.log('[Kill Test] ⚠️  Warning: No reconnections occurred during server kill test')
            }
        } catch (error) {
            console.error('[Kill Test] ❌ Server kill test failed:', error)
        }
    }

    async testResubscribeMechanism() {
        if (!this.enableReconnectionTest) {
            console.log('\n=== Skipping Reconnection Test (disabled) ===')

            return
        }

        console.log('\n=== Testing Resubscribe Mechanism ===')

        // Will enable connection drops after receiving some messages

        const stream = this.client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000,
            },
            resubscribe: {
                enabled: true,
                retries: 3,
                delay: 1000,
                backoff: 1.5,
                maxDelay: 5000,
                autoReset: true,
            },
        })

        let messageCount = 0
        let reconnectionCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Client Test] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)

            if (messageCount === 3) {
                console.log('[Client Test] 🔥 Enabling connection drops after 3 messages')
                this.server.enableConnectionDrops(1)
            }
        })

        stream.on('state', (state) => {
            console.log(`[Client Test] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Client Test] Stream error: ${error.message} (${Date.now() - startTime}ms)`)
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Client Test] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Client Test] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Client Test] ✅ Successfully resubscribed! (${Date.now() - startTime}ms)`)
            this.server.disableConnectionDrops()
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Client Test] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Client Test] ❌ Resubscribe abandoned: ${reason}`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Client Test] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)

            if (error) {
                console.log('[Client Test] Close error:', error)
            }
        })

        try {
            await stream.subscribe()
            console.log('[Client Test] ✅ Initial subscription successful')

            await new Promise<void>((resolve) => {
                let hasReconnected = false

                const onResubscribed = () => {
                    if (!hasReconnected) {
                        hasReconnected = true
                        console.log(`[Client Test] 🎯 Detected successful reconnection, waiting 3 more seconds...`)
                        setTimeout(() => resolve(), 3000)
                    }
                }

                stream.once('resubscribed', onResubscribed)

                setTimeout(() => {
                    if (!hasReconnected) {
                        console.log(`[Client Test] ⏰ Timeout reached without reconnection`)
                        stream.off('resubscribed', onResubscribed)
                        resolve()
                    }
                }, this.reconnectionTestDelay)
            })

            console.log('[Client Test] Closing stream after reconnection test...')
            await stream.close()
            console.log('[Client Test] ✅ Reconnection test completed')
            console.log(`[Client Test] Total messages: ${messageCount}, Reconnections: ${reconnectionCount}`)

            if (reconnectionCount === 0) {
                console.log('[Client Test] ⚠️  Warning: No reconnections occurred during test')
            }
        } catch (error) {
            console.error('[Client Test] ❌ Reconnection test failed:', error)
        }
    }

    public async runAllTests() {
        console.log('🚀 Starting ShredForwarder Client Tests')
        console.log(`Server: localhost:${this.serverPort}`)
        console.log(`Test duration: ${this.testDuration}ms`)
        console.log(`Reconnection test: ${this.enableReconnectionTest ? 'enabled' : 'disabled'}`)

        try {
            await this.server.start()
            console.log('✅ Mock server started')

            await this.testUnaryMethods()
            await this.testStreamingWithoutReconnection()
            await this.testConnectionDroppedException()
            await this.testServerProcessKill()
            await this.testServerKillScenario()
            await this.testResubscribeMechanism()

            console.log('\n🎉 All tests completed!')
        } catch (error) {
            console.error('❌ Test suite failed:', error)
        } finally {
            await this.cleanup()
        }
    }

    public async cleanup() {
        console.log('\n🧹 Cleaning up...')

        try {
            this.client.grpc.close()
            await this.server.stop()
            console.log('✅ Cleanup completed')
        } catch (error) {
            console.error('❌ Cleanup failed:', error)
        }
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new ShredForwarderClientTester({
        serverPort: 50_052,
        testDuration: 30_000,
        enableReconnectionTest: true,
        reconnectionTestDelay: 15_000,
    })

    process.on('SIGINT', async () => {
        console.log('\n🛑 Received SIGINT, cleaning up...')
        await tester.cleanup()
        process.exit(0)
    })

    tester.runAllTests().catch(console.error)
}
