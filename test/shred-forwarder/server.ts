/* eslint-disable */
import { Server, ServerCredentials, type ServerUnaryCall, type ServerWritableStream, type sendUnaryData } from '@grpc/grpc-js'
import { type PingRequest, type PongResponse, ShredForwarderServiceService, type SubscribeRequest, type SubscribeResponse, type VersionedTransaction } from '../../src/clients/shred-forwarder'

interface MockServerOptions {
    port?: number
    simulateConnectionDrops?: boolean
    dropAfterMessages?: number
    pingDelay?: number
    subscribeInterval?: number
}

export class MockShredForwarderServer {
    private readonly server: Server
    private readonly port: number
    private simulateConnectionDrops: boolean
    private dropAfterMessages: number
    private readonly pingDelay: number
    private readonly subscribeInterval: number
    private readonly messageCount = 0
    private readonly activeStreams = new Set<ServerWritableStream<SubscribeRequest, SubscribeResponse>>()

    public constructor(options: MockServerOptions = {}) {
        this.port = options.port ?? 50_051
        this.simulateConnectionDrops = options.simulateConnectionDrops ?? false
        this.dropAfterMessages = options.dropAfterMessages ?? 5
        this.pingDelay = options.pingDelay ?? 100
        this.subscribeInterval = options.subscribeInterval ?? 1000

        this.server = new Server()
        this.setupService()
    }

    private setupService() {
        this.server.addService(ShredForwarderServiceService, {
            ping: this.handlePing.bind(this),
            subscribe: this.handleSubscribe.bind(this),
        })
    }

    private async handlePing(
        call: ServerUnaryCall<PingRequest, PongResponse>,
        callback: sendUnaryData<PongResponse>,
    ) {
        const request = call.request
        console.log(`[Mock Server] Ping received with count: ${request.count}`)

        if (this.pingDelay > 0) {
            await new Promise((resolve) => setTimeout(resolve, this.pingDelay))
        }

        const response: PongResponse = {
            count: request.count,
            timestamp: new Date(),
            version: '1.0.0-mock',
        }

        console.log(`[Mock Server] Sending pong response: ${JSON.stringify(response)}`)
        callback(null, response)
    }

    private handleSubscribe(call: ServerWritableStream<SubscribeRequest, SubscribeResponse>) {
        console.log('[Mock Server] Subscribe stream started')
        this.activeStreams.add(call)

        let messagesSent = 0
        let slot = BigInt(100_000)

        const sendMessage = () => {
            if (call.destroyed || call.writableEnded) {
                return
            }

            if (this.simulateConnectionDrops && messagesSent >= this.dropAfterMessages) {
                console.log(`[Mock Server] Simulating connection drop after ${messagesSent} messages`)

                setTimeout(() => {
                    try {
                        call.emit('error', new Error('Simulated connection drop'))
                        call.destroy()
                    } catch (error) {
                        console.error('[Mock Server] Error during connection drop simulation:', error)
                    }
                }, 100)

                return
            }

            const mockTransaction: VersionedTransaction = {
                signatures: [Buffer.from(`mock_signature_${messagesSent}`, 'utf8')],
                message: {
                    legacy: {
                        header: {
                            numRequiredSignatures: 1,
                            numReadonlySignedAccounts: 0,
                            numReadonlyUnsignedAccounts: 1,
                        },
                        accountKeys: [
                            Buffer.from('11111111111111111111111111111112', 'base64'),
                            Buffer.from('22222222222222222222222222222223', 'base64'),
                        ],
                        recentBlockhash: Buffer.from(`mock_blockhash_${messagesSent}`, 'utf8'),
                        instructions: [{
                            programIdIndex: 0,
                            accounts: [1],
                            data: Buffer.from('mock_instruction_data', 'utf8'),
                        }],
                    },
                },
            }

            const response: SubscribeResponse = {
                slot: slot++,
                entryIndex: messagesSent % 10,
                transactionIndex: messagesSent % 5,
                transaction: mockTransaction,
                receivedAt: new Date(),
                processingTimeNanos: BigInt(Math.floor(Math.random() * 1_000_000)),
            }

            console.log(`[Mock Server] Sending message ${messagesSent + 1}: slot=${response.slot}`)

            try {
                call.write(response)
                messagesSent++
            } catch (error) {
                console.error('[Mock Server] Error writing to stream:', error)

                return
            }

            setTimeout(sendMessage, this.subscribeInterval)
        }

        call.on('cancelled', () => {
            console.log('[Mock Server] Subscribe stream cancelled')
            this.activeStreams.delete(call)
        })

        call.on('error', (error) => {
            console.log('[Mock Server] Subscribe stream error:', error.message)
            this.activeStreams.delete(call)
        })

        call.on('close', () => {
            console.log('[Mock Server] Subscribe stream closed')
            this.activeStreams.delete(call)
        })

        setTimeout(sendMessage, this.subscribeInterval)
    }

    public async start(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.server.bindAsync(
                `0.0.0.0:${this.port}`,
                ServerCredentials.createInsecure(),
                (error, port) => {
                    if (error) {
                        reject(error)

                        return
                    }

                    console.log(`[Mock Server] Started on port ${port}`)
                    this.server.start()
                    resolve()
                },
            )
        })
    }

    public async stop(): Promise<void> {
        return new Promise((resolve) => {
            console.log('[Mock Server] Stopping server...')

            for (const stream of this.activeStreams) {
                try {
                    stream.end()
                } catch (error) {
                    console.error('[Mock Server] Error ending stream:', error)
                }
            }

            this.activeStreams.clear()

            this.server.tryShutdown((error) => {
                if (error) {
                    console.error('[Mock Server] Error during shutdown:', error)
                    this.server.forceShutdown()
                } else {
                    console.log('[Mock Server] Stopped gracefully')
                }

                resolve()
            })
        })
    }

    public getAddress(): string {
        return `localhost:${this.port}`
    }

    public enableConnectionDrops(dropAfterMessages = 5) {
        this.simulateConnectionDrops = true
        this.dropAfterMessages = dropAfterMessages
        console.log(`[Mock Server] Connection drops enabled after ${dropAfterMessages} messages`)
    }

    public disableConnectionDrops() {
        this.simulateConnectionDrops = false
        console.log('[Mock Server] Connection drops disabled')
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    const server = new MockShredForwarderServer({
        port: 50_051,
        simulateConnectionDrops: false,
        subscribeInterval: 2000,
    })

    process.on('SIGINT', async () => {
        console.log('\nReceived SIGINT, shutting down...')
        await server.stop()
        process.exit(0)
    })

    server.start().then(() => {
        console.log('Mock ShredForwarder server is running. Press Ctrl+C to stop.')
    }).catch(console.error)
}
