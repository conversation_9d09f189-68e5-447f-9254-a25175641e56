/* eslint-disable */
import { ShredForwarderClient } from '../../src/clients/shred-forwarder'
import { MockShredForwarderServer } from './server'

async function debugResubscribe() {
    console.log('🔍 Debug Resubscribe Mechanism')
    
    const server = new MockShredForwarderServer({
        port: 50053,
        simulateConnectionDrops: false,
        subscribeInterval: 1000
    })

    const client = new ShredForwarderClient('http://localhost:50053')

    try {
        await server.start()
        console.log('✅ Mock server started')

        const stream = client.createStream({
            timeout: {
                subscribe: 5000,
                close: 3000
            },
            resubscribe: {
                enabled: true,
                retries: 3,
                delay: 1000,
                backoff: 1.5,
                maxDelay: 5000,
                autoReset: true
            }
        })

        let messageCount = 0
        let reconnectionCount = 0
        const startTime = Date.now()

        stream.on('data', (data) => {
            messageCount++
            console.log(`[Debug] Message ${messageCount}: slot=${data.slot} (${Date.now() - startTime}ms)`)
            
            if (messageCount === 3) {
                console.log('[Debug] 🔥 Enabling connection drops after 3 messages')
                server.enableConnectionDrops(1)
            }
        })

        stream.on('state', (state) => {
            console.log(`[Debug] Stream state: ${state} (${Date.now() - startTime}ms)`)
        })

        stream.on('error', (error) => {
            console.error(`[Debug] Stream error: ${error} (${Date.now() - startTime}ms)`)
        })

        stream.on('waitForResubscribe', (delay) => {
            console.log(`[Debug] 🔄 Waiting ${delay}ms before resubscribe (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribe', (attempt, retriesLeft) => {
            reconnectionCount++
            console.log(`[Debug] 🔄 Resubscribe attempt ${attempt}, retries left: ${retriesLeft} (${Date.now() - startTime}ms)`)
        })

        stream.on('resubscribed', () => {
            console.log(`[Debug] ✅ Successfully resubscribed! (${Date.now() - startTime}ms)`)
            server.disableConnectionDrops()
        })

        stream.on('circuitBreakerTripped', (lastSuccessTime) => {
            console.log(`[Debug] ⚡ Circuit breaker tripped, last success: ${new Date(lastSuccessTime).toISOString()}`)
        })

        stream.on('resubscribeAbandoned', (reason) => {
            console.log(`[Debug] ❌ Resubscribe abandoned: ${reason} (${Date.now() - startTime}ms)`)
        })

        stream.on('closed', (isExplicitly, error) => {
            console.log(`[Debug] Stream closed - explicitly: ${isExplicitly} (${Date.now() - startTime}ms)`)
            if (error) {
                console.log('[Debug] Close error:', error)
            }
        })

        await stream.subscribe()
        console.log('[Debug] ✅ Initial subscription successful')

        await new Promise<void>((resolve) => {
            setTimeout(() => {
                console.log('[Debug] 🛑 Test timeout, closing stream...')
                resolve()
            }, 20000)
        })

        await stream.close()
        console.log(`[Debug] ✅ Test completed - Messages: ${messageCount}, Reconnections: ${reconnectionCount}`)

    } catch (error) {
        console.error('[Debug] ❌ Test failed:', error)
    } finally {
        client.grpc.close()
        await server.stop()
        console.log('[Debug] ✅ Cleanup completed')
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    debugResubscribe().catch(console.error)
}
